<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ShipStationService;
use App\Models\Order;
use App\Models\OrderBox;
use Illuminate\Support\Facades\Log;

class ShippingController extends Controller
{
    protected $shipStationService;

    public function __construct(ShipStationService $shipStationService)
    {
        $this->shipStationService = $shipStationService;
    }

    /**
     * Get shipping rates for an order with individual box pricing
     */
    public function getShippingRates(Request $request, $orderId)
    {
        try {
            $order = Order::with(['boxes', 'cart_info.product', 'user'])->findOrFail($orderId);

            // Use the service method to get rates for the order
            $boxRates = $this->shipStationService->getRatesForOrder($order);

            // Format for legacy frontend compatibility
            $legacyFormat = $this->shipStationService->formatRatesForLegacyFrontend($boxRates);

            // Get additional analysis
            $cheapestOption = $this->shipStationService->getCheapestShippingOption($boxRates);
            $carrierOptions = $this->shipStationService->getShippingOptionsByCarrier($boxRates);

            return response()->json([
                'status' => true,
                'rates' => $legacyFormat['rates'], // For legacy frontend compatibility
                'package_info' => $legacyFormat['package_info'], // For legacy frontend compatibility
                'box_rates' => $boxRates, // New detailed box rates
                'box_details' => $legacyFormat['box_details'], // Box details
                'cheapest_option' => $cheapestOption,
                'carrier_options' => $carrierOptions,
                'order_info' => [
                    'id' => $order->id,
                    'order_number' => $order->order_number,
                    'total_boxes' => $order->boxes->count(),
                    'total_weight' => $legacyFormat['package_info']['total_weight']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Shipping rates error for order ' . $orderId . ': ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Error getting shipping rates: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Create a shipment for an order
     */
    public function createShipment(Request $request, $orderId)
    {
        try {
            $request->validate([
                'carrier_code' => 'required|string',
                'service_code' => 'required|string'
            ]);

            $order = Order::with('boxes', 'user')->findOrFail($orderId);
            
            // Calculate total dimensions and weight
            $totalWeight = $order->boxes->sum('weight') ?: 1;
            $estimatedLength = $order->boxes->max('length') ?: 12;
            $estimatedWidth = $order->boxes->max('width') ?: 12;
            $estimatedHeight = $order->boxes->sum('height') ?: 6;

            $orderData = [
                'customer_name' => $order->first_name . ' ' . $order->last_name,
                'company_name' => $order->company_name,
                'address1' => $order->address1 ?? $order->shipping_address,
                'address2' => $order->address2,
                'city' => $order->city ?? $order->shipping_city,
                'state' => $order->state ?? $order->shipping_state,
                'postal_code' => $order->post_code ?? $order->shipping_zip,
                'country' => $order->country ?? 'US',
                'phone' => $order->phone,
                'weight' => $totalWeight,
                'length' => $estimatedLength,
                'width' => $estimatedWidth,
                'height' => $estimatedHeight
            ];

            $shipment = $this->shipStationService->createShipment(
                $orderData,
                $request->carrier_code,
                $request->service_code
            );

            if (isset($shipment['error'])) {
                return response()->json([
                    'status' => false,
                    'message' => $shipment['error']
                ]);
            }

            // Update order with tracking information
            $order->update([
                'tracking_number' => $shipment['trackingNumber'] ?? null,
                'shipping_label_url' => $shipment['labelData'] ?? null,
                'carrier_code' => $request->carrier_code,
                'service_code' => $request->service_code,
                'shipping_cost' => $shipment['shipmentCost'] ?? 0,
                'status' => 'shipped'
            ]);

            return response()->json([
                'status' => true,
                'message' => 'Shipment created successfully',
                'shipment' => $shipment,
                'tracking_number' => $shipment['trackingNumber'] ?? null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error creating shipment: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test ShipStation connection
     */
    public function testConnection()
    {
        try {
            $isConnected = $this->shipStationService->testConnection();

            return response()->json([
                'status' => $isConnected,
                'message' => $isConnected ? 'ShipStation connection successful' : 'ShipStation connection failed',
                'config_check' => [
                    'api_key_set' => !empty(config('services.shipstation.api_key')),
                    'api_secret_set' => !empty(config('services.shipstation.api_secret')),
                    'base_url' => config('services.shipstation.base_url')
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'config_check' => [
                    'api_key_set' => !empty(config('services.shipstation.api_key')),
                    'api_secret_set' => !empty(config('services.shipstation.api_secret')),
                    'base_url' => config('services.shipstation.base_url')
                ]
            ]);
        }
    }

    /**
     * Debug shipping rates for testing
     */
    public function debugShippingRates($orderId)
    {
        try {
            $order = Order::with(['boxes', 'cart_info.product', 'user'])->findOrFail($orderId);

            Log::info('Debug shipping rates for order: ' . $orderId, [
                'order_id' => $order->id,
                'boxes_count' => $order->boxes->count(),
                'cart_items_count' => $order->cart_info->count(),
                'order_address' => [
                    'city' => $order->user ? $order->user->shipping_city : 'N/A',
                    'state' => $order->user ? $order->user->shipping_state : 'N/A',
                    'postal_code' => $order->post_code ?? ($order->user ? $order->user->shipping_zip : 'N/A'),
                    'address1' => $order->address1,
                    'address2' => $order->address2,
                    'country' => $order->country
                ]
            ]);

            // Get rates using the service
            $boxRates = $this->shipStationService->getRatesForOrder($order);

            Log::info('Box rates result:', ['box_rates' => $boxRates]);

            // Format for legacy frontend
            $legacyFormat = $this->shipStationService->formatRatesForLegacyFrontend($boxRates);

            return response()->json([
                'status' => true,
                'debug_info' => [
                    'order_id' => $order->id,
                    'boxes_count' => $order->boxes->count(),
                    'raw_box_rates' => $boxRates,
                    'legacy_format' => $legacyFormat
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Debug shipping rates error: ' . $e->getMessage(), [
                'order_id' => $orderId,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'status' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get available carriers and services
     */
    public function getCarriers()
    {
        $carriers = [
            'fedex' => [
                'name' => 'FedEx',
                'services' => [
                    'fedex_ground' => 'FedEx Ground',
                    'fedex_2_day' => 'FedEx 2Day',
                    'fedex_express_saver' => 'FedEx Express Saver',
                    'fedex_standard_overnight' => 'FedEx Standard Overnight',
                    'fedex_priority_overnight' => 'FedEx Priority Overnight'
                ]
            ],
            'ups' => [
                'name' => 'UPS',
                'services' => [
                    'ups_ground' => 'UPS Ground',
                    'ups_3_day_select' => 'UPS 3 Day Select',
                    'ups_2nd_day_air' => 'UPS 2nd Day Air',
                    'ups_next_day_air' => 'UPS Next Day Air',
                    'ups_next_day_air_saver' => 'UPS Next Day Air Saver'
                ]
            ],
            'stamps_com' => [
                'name' => 'USPS',
                'services' => [
                    'usps_first_class_mail' => 'USPS First-Class Mail',
                    'usps_priority_mail' => 'USPS Priority Mail',
                    'usps_priority_mail_express' => 'USPS Priority Mail Express',
                    'usps_ground_advantage' => 'USPS Ground Advantage',
                    'usps_media_mail' => 'USPS Media Mail'
                ]
            ]
        ];

        return response()->json([
            'status' => true,
            'carriers' => $carriers
        ]);
    }

    /**
     * Display shipping rates in the order packing interface
     */
    public function showRatesForOrder($orderId)
    {
        try {
            $order = Order::with('boxes')->findOrFail($orderId);
            
            return view('admin.shipping.rates', compact('order'));

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Order not found');
        }
    }

    /**
     * Get shipping rates for individual boxes
     */
    public function getBoxShippingRates(Request $request)
    {
        try {
            $request->validate([
                'boxes' => 'required|array|min:1',
                'boxes.*.weight' => 'required|numeric|min:0.1',
                'boxes.*.length' => 'numeric|min:1',
                'boxes.*.width' => 'numeric|min:1',
                'boxes.*.height' => 'numeric|min:1',
                'to_state' => 'required|string|max:2',
                'to_postal_code' => 'required|string|max:10'
            ]);

            $fromAddress = [
                'postal_code' => config('services.shipstation.from_address.postal_code'),
                'city' => config('services.shipstation.from_address.city'),
                'state' => config('services.shipstation.from_address.state'),
                'country' => config('services.shipstation.from_address.country', 'US')
            ];

            $toAddress = [
                'state' => $request->to_state,
                'postal_code' => $request->to_postal_code,
                'city' => $request->to_city,
                'country' => $request->to_country ?? 'US',
                'residential' => $request->residential ?? true
            ];

            // Format boxes for the service
            $boxes = [];
            foreach ($request->boxes as $index => $boxData) {
                $boxes[] = [
                    'length' => $boxData['length'] ?? 12,
                    'width' => $boxData['width'] ?? 12,
                    'height' => $boxData['height'] ?? 6,
                    'weight' => $boxData['weight'],
                    'weight_units' => 'pounds',
                    'dimension_units' => 'inches',
                    'box_name' => "Box " . ($index + 1)
                ];
            }

            // Get rates for each box
            $boxRates = $this->shipStationService->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);

            // Get cheapest option
            $cheapestOption = $this->shipStationService->getCheapestShippingOption($boxRates);

            // Get carrier options
            $carrierOptions = $this->shipStationService->getShippingOptionsByCarrier($boxRates);

            return response()->json([
                'status' => true,
                'box_rates' => $boxRates,
                'cheapest_option' => $cheapestOption,
                'carrier_options' => $carrierOptions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error getting box shipping rates: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get quick shipping estimate (updated for single box)
     */
    public function getQuickEstimate(Request $request)
    {
        try {
            $request->validate([
                'weight' => 'required|numeric|min:0.1',
                'to_state' => 'required|string|max:2',
                'to_postal_code' => 'required|string|max:10'
            ]);

            $fromAddress = [
                'postal_code' => config('services.shipstation.from_address.postal_code'),
                'city' => config('services.shipstation.from_address.city'),
                'state' => config('services.shipstation.from_address.state'),
                'country' => config('services.shipstation.from_address.country', 'US')
            ];

            $toAddress = [
                'state' => $request->to_state,
                'postal_code' => $request->to_postal_code,
                'country' => 'US',
                'residential' => true
            ];

            $boxes = [[
                'weight' => $request->weight,
                'length' => $request->length ?? 12,
                'width' => $request->width ?? 12,
                'height' => $request->height ?? 6,
                'weight_units' => 'pounds',
                'dimension_units' => 'inches'
            ]];

            $boxRates = $this->shipStationService->getShippingRatesForBoxes($fromAddress, $toAddress, $boxes);
            $cheapestOption = $this->shipStationService->getCheapestShippingOption($boxRates);

            return response()->json([
                'status' => true,
                'rates' => $boxRates['box_1']['rates'] ?? [],
                'cheapest_rate' => $cheapestOption
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error getting estimate: ' . $e->getMessage()
            ]);
        }
    }
}
